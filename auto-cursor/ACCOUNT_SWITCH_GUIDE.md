# Auto-Cursor 账号切换功能改进指南

## 🎯 改进内容

基于CursorPool_Client项目的完整实现，我已经为你的auto-cursor项目添加了完整的账号切换功能。

## 🔧 核心改进

### 1. 后端改进 (Rust)

#### 新增函数：
- `switch_account_with_token()` - 支持直接使用邮箱和token切换
- `inject_token_to_sqlite_with_auth_type()` - 支持自定义认证类型
- 改进的 `inject_email_to_sqlite()` 和 `inject_token_to_sqlite()` 函数

#### 关键数据库字段更新：
```rust
let auth_fields = vec![
    ("cursorAuth/accessToken", processed_token),
    ("cursorAuth/refreshToken", processed_token),    // refreshToken = accessToken
    ("cursor.accessToken", processed_token),         // 额外的token字段
    ("cursorAuth/cachedSignUpType", auth_type),      // 认证类型 - 关键！
];

let email_fields = vec![
    ("cursorAuth/cachedEmail", email),  // 主要邮箱字段
    ("cursor.email", email),            // 额外的邮箱字段
];
```

#### Token处理逻辑：
```rust
// 处理包含分隔符的token
let processed_token = if token.contains("%3A%3A") {
    token.split("%3A%3A").nth(1).unwrap_or(token)
} else if token.contains("::") {
    token.split("::").nth(1).unwrap_or(token)
} else {
    token
};
```

### 2. 前端改进 (TypeScript/React)

#### 新增服务方法：
```typescript
// 直接使用邮箱和token切换账号
static async switchAccountWithToken(
  email: string, 
  token: string, 
  authType?: string
): Promise<SwitchAccountResult>
```

#### 新增UI功能：
- 🚀 **快速切换** 按钮和表单
- 支持选择认证类型 (Auth_0/Google/GitHub)
- 更好的用户体验和错误提示

## 🚀 使用方法

### 方法1：快速切换 (推荐)
1. 点击 "🚀 快速切换" 按钮
2. 输入邮箱和token
3. 选择认证类型 (默认Auth_0)
4. 点击 "🚀 立即切换"
5. 重启Cursor查看效果

### 方法2：传统方式
1. 先添加账户到列表
2. 从列表中选择账户切换

## 🔍 关键发现

### RefreshToken的真相：
- **RefreshToken实际上就是AccessToken** - 它们是同一个JWT token
- CursorPool_Client项目中，两个字段存储的是相同的值
- 这解释了为什么你之前只更新token但Cursor仍显示未登录

### 必须更新的数据库字段：
1. `cursorAuth/cachedEmail` - **最重要**，Cursor识别登录状态的关键
2. `cursorAuth/accessToken` - 访问令牌
3. `cursorAuth/refreshToken` - 刷新令牌 (与accessToken相同)
4. `cursorAuth/cachedSignUpType` - 认证类型 (Auth_0/Google/GitHub)
5. `cursor.email` - 额外的邮箱字段
6. `cursor.accessToken` - 额外的token字段

## 🛠️ 编译和运行

```bash
# 进入项目目录
cd auto-cursor

# 安装依赖
npm install

# 开发模式运行
npm run tauri dev

# 构建生产版本
npm run tauri build
```

## 🐛 故障排除

### 如果切换后仍显示未登录：
1. 检查token格式是否正确
2. 确认所有数据库字段都已更新
3. 重启Cursor应用
4. 检查Cursor数据库路径是否正确

### 常见Token格式：
- 完整格式: `user_01XXXXXXX%3A%3AeyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...`
- 处理后: `eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...` (JWT格式)

## 📝 测试建议

1. 使用有效的Cursor账号token进行测试
2. 在切换前备份Cursor配置
3. 测试不同的认证类型
4. 验证切换后的登录状态

## 🎉 总结

现在你的auto-cursor项目具备了与CursorPool_Client相同的完整账号切换功能：

- ✅ 完整的数据库字段更新
- ✅ 正确的token处理逻辑
- ✅ 支持多种认证类型
- ✅ 用户友好的界面
- ✅ 事务安全的数据库操作

这应该能解决你之前遇到的"在cursor还是显示未登录"的问题！
